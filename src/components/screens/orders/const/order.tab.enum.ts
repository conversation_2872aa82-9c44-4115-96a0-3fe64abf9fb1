import { Order } from '@Type/order-api.interface';
import { cloneDeep } from 'lodash';
import { ID_USER_SYSTEM } from 'src/constants/account';
import { SourceEntity } from 'src/enum/order-api.enum';
import { OrderType } from 'src/enum/order-type.enum';

export enum OrderStatusEnum {
  Draft = 'Draft', // đơn nháp // Đơn tạo bất kỳ có một vài thông tin cơ bản.
  New = 'New', // đơn mới // Đơn có đầy đủ các thông tin cơ bản và bắt buộc.
  AwaitingStock = 'AwaitingStock', // chờ hàng // ":vertical_traffic_light:Trạng thái tự động. Đơn đã được xác nhận nhưng thiếu hàng trong kho thì hệ thống tự động chuyển sang trạng thái này."
  Reconfirm = 'Reconfirm', // xác nhận lại // ":vertical_traffic_light:Tr<PERSON><PERSON> thái tự động. <PERSON><PERSON>n sau khi được có sản phẩm đủ tồn kho, sẽ tự động chuyển sang trạng thái này"
  Confirmed = 'Confirmed', // đã xác nhận // Chuyển danh sách đơn xác nhận sang cho kho lấy hàng đóng gói.
  Preparing = 'Preparing', // đang chuẩn bị hàng // Đơn được lên danh sách chờ lấy hàng ra khỏi kệ trong kho.
  HandlingOver = 'HandlingOver', // Đang bàn giao vận chuyển // Đơn đã chuẩn bị hàng xong, chờ 3Pl đến lấy.
  InTransit = 'InTransit', // Đang vận chuyển. // Đơn đã bàn giao cho 3PL.
  InDelivery = 'InDelivery', // Đang giao. // Đơn đang được giao cho khách.
  Delivered = 'Delivered', // Đã giao (chờ đối soát). Đơn đã được giao tới khách.
  DeliveredCompleted = 'DeliveredCompleted', // Đã giao (Hoàn tất) // Đơn đã xong, Giao đã thanh toán xong cho Khách.
  FailedDelivery = 'FailedDelivery', // Giao thất bại
  AwaitingReturn = 'AwaitingReturn', // Chờ hoàn. Đơn bị NN từ chối và quá số lần giao/lưu kho.
  InReturn = 'InReturn', // Đang hoàn. Đơn đang được 3PL hoàn về.
  ReturnedStocked = 'ReturnedStocked', // Đã hoàn (Chờ đối soát). Đơn hoàn đã tái nhập kho.
  ReturnedCompleted = 'ReturnedCompleted', // Đã hoàn (Hoàn tất). Đơn hoàn đã đối soát xong.
  // DamagedLost = "DamagedLost", // Hư hỏng, thất lạc (Chờ xử lý). Đơn hư hỏng, thất lạc vì kho bảo quản không tốt hoặc do 3PL...
  // DamagedLostCompleted = 'DamagedLostCompleted', // Hư hỏng, thất lạc (Hoàn tất). Đơn hư hỏng, thất lạc bởi kho hoặc 3PL đã truy thu/ xử lý tài chính xong.
  Damaged = 'Damaged', // Hư hỏng (Chờ xử lý). Đơn hư hỏng vì kho bảo quản không tốt hoặc do 3PL...
  DamagedCompleted = 'DamagedCompleted', // Hư hỏng, thất lạc (Hoàn tất). Đơn hư hỏng, thất lạc bởi kho hoặc 3PL đã truy thu/ xử lý tài chính xong.
  Canceled = 'Canceled', // Hủy. Đơn được hủy trước khi bàn giao cho 3PL.
  Lost = 'Lost', // Thất lạc (Chờ xử lý). Đơn thất lạc vì kho bảo quản không tốt hoặc do 3PL...
  LostCompleted = 'LostCompleted', // Thất lạc (Hoàn tất). Đơn thất lạc bởi kho hoặc 3PL đã truy thu/ xử lý tài chính xong.
}

export enum OrderBEStatus {
  Draft = -2, // đơn nháp // Đơn tạo bất kỳ có một vài thông tin cơ bản.
  New = 0, // đơn mới // Đơn có đầy đủ các thông tin cơ bản và bắt buộc.
  AwaitingStock = 1, // chờ hàng // "🚦Trạng thái tự động. Đơn đã được xác nhận nhưng thiếu hàng trong kho thì hệ thống tự động chuyển sang trạng thái này."
  Reconfirm = 2, // xác nhận lại // "🚦Trạng thái tự động. Đơn sau khi được có sản phẩm đủ tồn kho, sẽ tự động chuyển sang trạng thái này"
  Confirmed = 3, // đã xác nhận // Chuyển danh sách đơn xác nhận sang cho kho lấy hàng đóng gói.
  Preparing = 4, // đang chuẩn bị hàng // Đơn được lên danh sách chờ lấy hàng ra khỏi kệ trong kho.
  HandlingOver = 5, // Đang bàn giao vận chuyển // Đơn đã chuẩn bị hàng xong, chờ 3Pl đến lấy.
  InTransit = 6, // Đang vận chuyển. // Đơn đã bàn giao cho 3PL.
  InDelivery = 7, // Đang giao. // Đơn đang được giao cho khách.
  Delivered = 8, // Đã giao (chờ đối soát). Đơn đã được giao tới khách.
  DeliveredCompleted = 9, // Đã giao (Hoàn tất) // Đơn đã xong, Giao đã thanh toán xong cho Khách.
  FailedDelivery = 10, // Giao thất bại
  AwaitingReturn = 11, // Chờ hoàn. Đơn bị NN từ chối và quá số lần giao/lưu kho.
  InReturn = 12, // Đang hoàn. Đơn đang được 3PL hoàn về.
  ReturnedStocked = 13, // Đã hoàn (Chờ đối soát). Đơn hoàn đã tái nhập kho.
  ReturnedCompleted = 14, // Đã hoàn (Hoàn tất). Đơn hoàn đã đối soát xong.
  // DamagedLost = 15, // Hư hỏng, thất lạc (Chờ xử lý). Đơn hư hỏng, thất lạc vì kho bảo quản không tốt hoặc do 3PL...
  // DamagedLostCompleted = 16, // Hư hỏng, thất lạc (Hoàn tất). Đơn hư hỏng, thất lạc bởi kho hoặc 3PL đã truy thu/ xử lý tài chính xong.
  Damaged = 15, // Hư hỏng (Chờ xử lý). Đơn hư hỏng vì kho bảo quản không tốt hoặc do 3PL...
  DamagedCompleted = 16, // Hư hỏng, thất lạc (Hoàn tất). Đơn hư hỏng, thất lạc bởi kho hoặc 3PL đã truy thu/ xử lý tài chính xong.
  Canceled = 17, // Hủy. Đơn được hủy trước khi bàn giao cho 3PL.
  Lost = 18, // Thất lạc (Chờ xử lý). Đơn thất lạc vì kho bảo quản không tốt hoặc do 3PL...
  LostCompleted = 19, // Thất lạc (Hoàn tất). Đơn thất lạc bởi kho hoặc 3PL đã truy thu/ xử lý tài chính xong.
}

export const EnableEditStatusOrder = [
  OrderStatusEnum.Draft,
  OrderStatusEnum.New,
  OrderStatusEnum.Reconfirm,
  OrderStatusEnum.AwaitingStock,
  OrderStatusEnum.Confirmed,
];

export const EnableCancelOrder = [
  OrderStatusEnum.Draft,
  OrderStatusEnum.New,
  OrderStatusEnum.Reconfirm,
  OrderStatusEnum.AwaitingStock,
  OrderStatusEnum.Confirmed,
  OrderStatusEnum.Preparing,
  OrderStatusEnum.HandlingOver,
];

export const StatusWithOutAction = [];

export const TeleSaleStatus = [
  {
    title: 'Đơn nháp',
    value: 'Draft',
    code: OrderStatusEnum.Draft,
  },
  {
    title: 'Đơn hàng mới',
    value: 'New',
    code: 0,
    color: '#00a2ae',
  },
  {
    title: 'Đã xác nhận',
    value: 'Confirmed',
    code: 2,
    color: '#108EE9',
  },
  {
    title: 'Đã hủy',
    value: 'Cancelled',
    code: 12,
    color: '#d73435',
  },
];

export const DraftOrderStatusEnable = [
  // cần sửa

  {
    title: 'Đơn nháp',
    value: 'Draft',
    code: -2,
    color: '#ff7875',
  },
  {
    title: 'Đơn mới',
    value: 'New',
    code: 0,
    color: '#00a2ae',
  },
  {
    title: 'Đã hủy',
    value: 'Cancelled',
    code: 12,
    color: '#d73435',
  },
];

export const NewOrderInChat = [
  // cần sửa

  {
    title: 'Đơn nháp',
    value: 'Draft',
    code: -2,
    color: '#ff7875',
  },
  {
    title: 'Đơn mới',
    value: 'New',
    code: 0,
    color: '#00a2ae',
  },
  // {
  //     title: 'Chờ hàng',
  //     value: "WaitingRestock",
  //     code: 1,
  //     color:'#b49f09'
  // },
  // {
  //     title: 'Xác nhận lại',
  //     value: "NeedConfirm",
  //     code: 14,
  //     color: '#108EE9'
  // },
  // {
  //     title: 'Đã xác nhận',
  //     value: "Confirmed",
  //     code: 2,
  //     color: '#108EE9'
  // },
  // {
  //     title: 'Xác nhận sưu tập hàng',
  //     value: "CollectingConfirmed",
  //     code: 15,
  //     color: '#108EE9'
  // },
];

export const UpdateOrderStatusInNewOrder = [
  // cần sửa
  {
    title: 'Đơn mới',
    value: 'New',
    code: 0,
  },
  {
    title: 'Xác nhận lại',
    value: 'NeedConfirm',
    code: 14,
    color: '#108EE9',
  },
  {
    title: 'Đã xác nhận',
    value: 'Confirmed',
    code: 2,
    color: '#108EE9',
  },
  {
    title: 'Xác nhận sưu tập hàng',
    value: 'CollectingConfirmed',
    code: 15,
    color: '#108EE9',
  },
  {
    title: 'Đã hủy',
    value: 'Cancelled',
    code: 12,
    color: '#d73435',
  },
];
export const DraftFilterStatus = [
  {
    title: 'Đơn nháp',
    value: 'Draft',
    code: OrderStatusEnum.Draft,
    color: '#ff7875',
  },
];

const ColorButton = {
  all: {
    dotColor: '#000',
    bgColor: '#000',
  },
  yellow: {
    bgColor: '#FFEAC0',
    dotColor: '#EFBE3F',
  },
  green: {
    // xanh lá
    dotColor: '#10BEB4',
    bgColor: '#DCF5EB',
  },
  blue: {
    bgColor: '#E0E8FF',
    dotColor: '#0062FF',
  },
  red: {
    bgColor: '#FFE3DF',
    dotColor: '#E11900',
  },
  gray: {
    bgColor: '#EBEDF4',
    dotColor: '#000',
  },
};

const ColorButtonNewTabInsight = {
  all: {
    dotColor: '#000',
    bgColor: '#000',
  },
  yellow: {
    bgColor: '#FFEAC0',
    dotColor: '#FFE2A9',
  },
  green: {
    // xanh lá
    dotColor: '#ACF1D6',
    bgColor: '#DCF5EB',
  },
  blue: {
    bgColor: '#E0E8FF',
    dotColor: '#B3CFFF',
  },
  red: {
    bgColor: '#FFE3DF',
    dotColor: '#FFCEC8',
  },
  gray: {
    bgColor: '#EBEDF4',
    dotColor: '#E3E5EE',
  },
};

export const OrderStatusConfig = {
  All: {
    value: -1,
    ...ColorButton.all,
    vi: 'Tất cả',
    en: 'All',
    nextStatus: [],
  },
  [OrderStatusEnum.Draft]: {
    // đã huỷ
    value: OrderStatusEnum.Draft,
    ...ColorButton.gray,
    vi: 'Nháp',
    en: 'Draft',
    nextStatus: [
      OrderStatusEnum.New,
      OrderStatusEnum.Confirmed,
      OrderStatusEnum.Canceled,
    ],
  },

  [OrderStatusEnum.New]: {
    // mới
    value: OrderStatusEnum.New,
    ...ColorButton.green,
    vi: 'Mới',
    en: 'New',
    nextStatus: [OrderStatusEnum.Confirmed, OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.AwaitingStock]: {
    value: OrderStatusEnum.AwaitingStock,
    ...ColorButton.yellow,
    vi: 'Chờ hàng',
    en: 'Awaiting Stock',
    nextStatus: [OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.Reconfirm]: {
    value: OrderStatusEnum.Reconfirm,
    ...ColorButton.green,
    vi: 'Xác nhận lại',
    en: 'Reconfirm',
    nextStatus: [OrderStatusEnum.Confirmed, OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.Confirmed]: {
    value: OrderStatusEnum.Confirmed,
    ...ColorButton.green,
    vi: 'Đã xác nhận',
    en: 'Confirmed',
    nextStatus: [OrderStatusEnum.Preparing, OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.Preparing]: {
    value: OrderStatusEnum.Preparing,
    ...ColorButton.yellow,
    vi: 'Đang chuẩn bị hàng',
    en: 'Preparing',
    nextStatus: [OrderStatusEnum.HandlingOver, OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.HandlingOver]: {
    value: OrderStatusEnum.HandlingOver,
    ...ColorButton.blue,
    vi: 'Đang bàn giao vận chuyển',
    en: 'Handling Over',
    nextStatus: [
      OrderStatusEnum.InTransit,
      OrderStatusEnum.Damaged,
      OrderStatusEnum.Lost,
      OrderStatusEnum.Canceled,
    ],
  },
  [OrderStatusEnum.InTransit]: {
    value: OrderStatusEnum.InTransit,
    ...ColorButton.green,
    vi: 'Đang vận chuyển',
    en: 'In-Transit',
    nextStatus: [OrderStatusEnum.InDelivery, OrderStatusEnum.Lost],
  },
  [OrderStatusEnum.InDelivery]: {
    value: OrderStatusEnum.InDelivery,
    ...ColorButton.blue,
    vi: 'Đang giao',
    en: 'In-Delivery',
    nextStatus: [
      OrderStatusEnum.Delivered,
      OrderStatusEnum.FailedDelivery,
      OrderStatusEnum.Lost,
    ],
  },
  [OrderStatusEnum.FailedDelivery]: {
    value: OrderStatusEnum.FailedDelivery,
    ...ColorButton.green,
    vi: 'Giao thất bại',
    en: 'Failed Delivery',
    nextStatus: [
      OrderStatusEnum.AwaitingReturn,
      OrderStatusEnum.InReturn,
      OrderStatusEnum.Delivered,
      OrderStatusEnum.Lost,
    ],
  },
  [OrderStatusEnum.Delivered]: {
    value: OrderStatusEnum.Delivered,
    ...ColorButton.green,
    vi: 'Đã giao (Chờ đối soát)',
    en: 'Delivered',
    nextStatus: [OrderStatusEnum.DeliveredCompleted],
  },
  [OrderStatusEnum.DeliveredCompleted]: {
    value: OrderStatusEnum.DeliveredCompleted,
    ...ColorButton.gray,
    vi: 'Đã giao (Hoàn tất)',
    en: 'Delivered (Completed)',
    nextStatus: [],
  },
  [OrderStatusEnum.AwaitingReturn]: {
    value: OrderStatusEnum.AwaitingReturn,
    ...ColorButton.yellow,
    vi: 'Chờ hoàn',
    en: 'Awaiting Return',
    nextStatus: [
      OrderStatusEnum.InReturn,
      OrderStatusEnum.InDelivery,
      OrderStatusEnum.Lost,
    ],
  },
  [OrderStatusEnum.InReturn]: {
    value: OrderStatusEnum.InReturn,
    ...ColorButton.blue,
    vi: 'Đang hoàn',
    en: 'In Return',
    nextStatus: [OrderStatusEnum.ReturnedStocked, OrderStatusEnum.Lost],
  },
  [OrderStatusEnum.ReturnedStocked]: {
    value: OrderStatusEnum.ReturnedStocked,
    ...ColorButton.green,
    vi: 'Đã hoàn (Chờ đối soát)',
    en: 'Returned (Stocked)',
    nextStatus: [
      OrderStatusEnum.ReturnedCompleted,
      OrderStatusEnum.Damaged,
      OrderStatusEnum.Lost,
    ],
  },
  [OrderStatusEnum.ReturnedCompleted]: {
    value: OrderStatusEnum.ReturnedCompleted,
    ...ColorButton.gray,
    vi: 'Đã hoàn (Hoàn tất)',
    en: 'Returned (Completed)',
    nextStatus: [],
  },
  [OrderStatusEnum.Damaged]: {
    value: OrderStatusEnum.Damaged,
    ...ColorButton.red,
    vi: 'Hư hỏng (Chờ xử lý)',
    en: 'Damaged',
    nextStatus: [OrderStatusEnum.DamagedCompleted],
  },
  [OrderStatusEnum.DamagedCompleted]: {
    value: OrderStatusEnum.DamagedCompleted,
    ...ColorButton.red,
    vi: 'Hư hỏng (Hoàn tất)',
    en: 'Damaged (Completed)',
    nextStatus: [],
  },
  [OrderStatusEnum.Lost]: {
    value: OrderStatusEnum.Lost,
    ...ColorButton.red,
    vi: 'Thất lạc (Chờ xử lý)',
    en: 'Lost',
    nextStatus: [OrderStatusEnum.LostCompleted],
  },
  [OrderStatusEnum.LostCompleted]: {
    value: OrderStatusEnum.LostCompleted,
    ...ColorButton.red,
    vi: 'Thất lạc (Hoàn tất)',
    en: 'Lost (Completed)',
    nextStatus: [],
  },
  [OrderStatusEnum.Canceled]: {
    // đã huỷ
    value: OrderStatusEnum.Canceled,
    ...ColorButton.gray,
    vi: 'Hủy',
    en: 'Canceled',
    nextStatus: [],
  },
};

export const OrderStatusConfigInsight = {
  All: {
    value: -1,
    ...ColorButton.all,
    vi: 'Tất cả',
    en: 'All',
    nextStatus: [],
  },
  [OrderStatusEnum.Draft]: {
    // đã huỷ
    value: OrderStatusEnum.Draft,
    ...ColorButtonNewTabInsight.gray,
    vi: 'Nháp',
    en: 'Draft',
    nextStatus: [
      OrderStatusEnum.New,
      OrderStatusEnum.Confirmed,
      OrderStatusEnum.Canceled,
    ],
  },

  [OrderStatusEnum.New]: {
    // mới
    value: OrderStatusEnum.New,
    ...ColorButtonNewTabInsight.green,
    vi: 'Mới',
    en: 'New',
    nextStatus: [OrderStatusEnum.Confirmed, OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.AwaitingStock]: {
    value: OrderStatusEnum.AwaitingStock,
    ...ColorButtonNewTabInsight.yellow,
    vi: 'Chờ hàng',
    en: 'Awaiting Stock',
    nextStatus: [OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.Reconfirm]: {
    value: OrderStatusEnum.Reconfirm,
    ...ColorButtonNewTabInsight.green,
    vi: 'Xác nhận lại',
    en: 'Reconfirm',
    nextStatus: [OrderStatusEnum.Confirmed, OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.Confirmed]: {
    value: OrderStatusEnum.Confirmed,
    ...ColorButtonNewTabInsight.green,
    vi: 'Đã xác nhận',
    en: 'Confirmed',
    nextStatus: [OrderStatusEnum.Preparing, OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.Preparing]: {
    value: OrderStatusEnum.Preparing,
    ...ColorButtonNewTabInsight.blue,
    vi: 'Đang chuẩn bị hàng',
    en: 'Preparing',
    nextStatus: [OrderStatusEnum.HandlingOver, OrderStatusEnum.Canceled],
  },
  [OrderStatusEnum.HandlingOver]: {
    value: OrderStatusEnum.HandlingOver,
    ...ColorButtonNewTabInsight.blue,
    vi: 'Đang bàn giao vận chuyển',
    en: 'Handling Over',
    nextStatus: [
      OrderStatusEnum.InTransit,
      OrderStatusEnum.Damaged,
      OrderStatusEnum.Lost,
      OrderStatusEnum.Canceled,
    ],
  },
  [OrderStatusEnum.InTransit]: {
    value: OrderStatusEnum.InTransit,
    ...ColorButtonNewTabInsight.blue,
    vi: 'Đang vận chuyển',
    en: 'In-Transit',
    nextStatus: [OrderStatusEnum.InDelivery, OrderStatusEnum.Lost],
  },
  [OrderStatusEnum.InDelivery]: {
    value: OrderStatusEnum.InDelivery,
    ...ColorButtonNewTabInsight.blue,
    vi: 'Đang giao',
    en: 'In-Delivery',
    nextStatus: [
      OrderStatusEnum.Delivered,
      OrderStatusEnum.FailedDelivery,
      OrderStatusEnum.Lost,
    ],
  },
  [OrderStatusEnum.FailedDelivery]: {
    value: OrderStatusEnum.FailedDelivery,
    ...ColorButtonNewTabInsight.yellow,
    vi: 'Giao thất bại',
    en: 'Failed Delivery',
    nextStatus: [
      OrderStatusEnum.AwaitingReturn,
      OrderStatusEnum.InReturn,
      OrderStatusEnum.Delivered,
      OrderStatusEnum.Lost,
    ],
  },
  [OrderStatusEnum.Delivered]: {
    value: OrderStatusEnum.Delivered,
    ...ColorButtonNewTabInsight.green,
    vi: 'Đã giao (Chờ đối soát)',
    en: 'Delivered',
    nextStatus: [OrderStatusEnum.DeliveredCompleted],
  },
  [OrderStatusEnum.DeliveredCompleted]: {
    value: OrderStatusEnum.DeliveredCompleted,
    ...ColorButtonNewTabInsight.gray,
    vi: 'Đã giao (Hoàn tất)',
    en: 'Delivered (Completed)',
    nextStatus: [],
  },
  [OrderStatusEnum.AwaitingReturn]: {
    value: OrderStatusEnum.AwaitingReturn,
    ...ColorButtonNewTabInsight.yellow,
    vi: 'Chờ hoàn',
    en: 'Awaiting Return',
    nextStatus: [
      OrderStatusEnum.InReturn,
      OrderStatusEnum.InDelivery,
      OrderStatusEnum.Lost,
    ],
  },
  [OrderStatusEnum.InReturn]: {
    value: OrderStatusEnum.InReturn,
    ...ColorButtonNewTabInsight.blue,
    vi: 'Đang hoàn',
    en: 'In Return',
    nextStatus: [OrderStatusEnum.ReturnedStocked, OrderStatusEnum.Lost],
  },
  [OrderStatusEnum.ReturnedStocked]: {
    value: OrderStatusEnum.ReturnedStocked,
    ...ColorButtonNewTabInsight.green,
    vi: 'Đã hoàn (Chờ đối soát)',
    en: 'Returned (Stocked)',
    nextStatus: [
      OrderStatusEnum.ReturnedCompleted,
      OrderStatusEnum.Damaged,
      OrderStatusEnum.Lost,
    ],
  },
  [OrderStatusEnum.ReturnedCompleted]: {
    value: OrderStatusEnum.ReturnedCompleted,
    ...ColorButtonNewTabInsight.gray,
    vi: 'Đã hoàn (Hoàn tất)',
    en: 'Returned (Completed)',
    nextStatus: [],
  },
  [OrderStatusEnum.Damaged]: {
    value: OrderStatusEnum.Damaged,
    ...ColorButtonNewTabInsight.red,
    vi: 'Hư hỏng (Chờ xử lý)',
    en: 'Damaged',
    nextStatus: [OrderStatusEnum.DamagedCompleted],
  },
  [OrderStatusEnum.DamagedCompleted]: {
    value: OrderStatusEnum.DamagedCompleted,
    ...ColorButtonNewTabInsight.red,
    vi: 'Hư hỏng (Hoàn tất)',
    en: 'Damaged (Completed)',
    nextStatus: [],
  },
  [OrderStatusEnum.Lost]: {
    value: OrderStatusEnum.Lost,
    ...ColorButtonNewTabInsight.red,
    vi: 'Thất lạc (Chờ xử lý)',
    en: 'Lost',
    nextStatus: [OrderStatusEnum.LostCompleted],
  },
  [OrderStatusEnum.LostCompleted]: {
    value: OrderStatusEnum.LostCompleted,
    ...ColorButtonNewTabInsight.red,
    vi: 'Thất lạc (Hoàn tất)',
    en: 'Lost (Completed)',
    nextStatus: [],
  },
  [OrderStatusEnum.Canceled]: {
    // đã huỷ
    value: OrderStatusEnum.Canceled,
    ...ColorButtonNewTabInsight.gray,
    vi: 'Hủy',
    en: 'Canceled',
    nextStatus: [],
  },
};

export const OrderStatus = [
  OrderStatusConfig.All,
  OrderStatusConfig[OrderStatusEnum.Draft],
  OrderStatusConfig[OrderStatusEnum.New],
  OrderStatusConfig[OrderStatusEnum.AwaitingStock],
  OrderStatusConfig[OrderStatusEnum.Reconfirm],
  OrderStatusConfig[OrderStatusEnum.Confirmed],
  OrderStatusConfig[OrderStatusEnum.Preparing],
  OrderStatusConfig[OrderStatusEnum.HandlingOver],
  OrderStatusConfig[OrderStatusEnum.InTransit],
  OrderStatusConfig[OrderStatusEnum.InDelivery],
  OrderStatusConfig[OrderStatusEnum.FailedDelivery],
  OrderStatusConfig[OrderStatusEnum.Delivered],
  OrderStatusConfig[OrderStatusEnum.DeliveredCompleted],

  OrderStatusConfig[OrderStatusEnum.AwaitingReturn],
  OrderStatusConfig[OrderStatusEnum.InReturn],
  OrderStatusConfig[OrderStatusEnum.ReturnedStocked],
  OrderStatusConfig[OrderStatusEnum.ReturnedCompleted],
  // OrderStatusConfig[OrderStatusEnum.DamagedLost],
  // OrderStatusConfig[OrderStatusEnum.DamagedLostCompleted],
  OrderStatusConfig[OrderStatusEnum.Damaged],
  OrderStatusConfig[OrderStatusEnum.DamagedCompleted],
  OrderStatusConfig[OrderStatusEnum.Lost],
  OrderStatusConfig[OrderStatusEnum.LostCompleted],
  OrderStatusConfig[OrderStatusEnum.Canceled],
];

// export enum DiscountType {
//     fixed = 'fixed',
//     percentage = '',
//   }

export const NewOrderState = {
  products: [],
  saleId: null,
  customerName: '',
  customerPhone: '',
  addressText: '',
  addressNote: '',
  addressWardId: null,
  addressDistrictId: null,
  addressProvinceId: null,
  status: OrderStatusEnum.New,
  carrierId: 0,
  discount: 0,
  surcharge: 0,
  shippingFee: 0,
  paid: 0,
  totalPrice: 0,
  currency: 'VND',
  note: '',
  tagIds: [],
  warehouseId: null,
  postCode: null,
  expectDeliveryAt: null,
  temporaryNote: '',
  percentage: 0,
  type: OrderType.normal,
};

export const defaultColumns = [
  {
    lablel: 'Mã ĐH',
    value: 'displayId',
    key: 'display_id',
  },
  {
    lablel: 'SO Code',
    value: 'ffmDisplayId',
    key: 'ffmDisplayId',
  },
  {
    lablel: 'Mã vận đơn',
    value: 'waybillNumber',
    key: 'table.waybill',
  },

  {
    lablel: 'Người phụ trách',
    value: 'sale',
    key: 'user_incharge',
  },
  {
    lablel: 'Nhân viên MKT',
    value: 'marketerId',
    key: 'filter.marketer',
  },
  {
    lablel: 'Khách hàng',
    value: 'customerName',
    key: 'customer_name',
  },
  {
    lablel: 'Số điện thoại',
    value: 'customerPhone',
    key: 'customer_phone',
  },
  {
    lablel: 'Địa chỉ',
    value: 'address',
    key: 'address',
  },
  {
    lablel: 'Sản phẩm',
    value: 'products',
    key: 'products',
  },
  {
    lablel: 'Tổng tiền',
    value: 'totalPrice',
    key: 'total_price',
  },
  {
    lablel: 'Thẻ',
    value: 'tags',
    key: 'tag',
  },
  {
    lablel: 'Ghi chú',
    value: 'note',
    key: 'note',
  },
  {
    lablel: 'Nguồn đơn',
    value: 'sourceId',
    key: 'source',
  },
  {
    lablel: 'Tạo lúc',
    value: 'createAt',
    key: 'created_at',
  },
  {
    lablel: 'Cập nhật',
    value: 'updatedAt',
    key: 'updated_at',
  },
  {
    lablel: 'Đổi trạng thái',
    value: 'lastUpdateStatus',
    key: 'lastUpdateStatus',
  },
  {
    lablel: 'Ngày nhận mong muốn',
    value: 'expect_delivery_at',
    key: 'expect_delivery_at',
  },
  {
    lablel: 'Thời điểm xác nhận',
    value: 'confirmation_time',
    key: 'confirmationtime',
  },
  {
    lablel: 'Thời điểm bắt đầu vận chuyển',
    value: 'shipping_start_time',
    key: 'shippingStartTime',
  },
  {
    lablel: 'Thời điểm khởi tạo hội thoại',
    value: 'convestation_start_time',
    key: 'convestationStartTime',
  },
  {
    lablel: 'Trạng thái',
    value: 'status',
    key: 'status',
  },
];

export const formatData = (dataDetail: Order, userId?: any) => {
  const newData = cloneDeep(dataDetail);
  const total = Number(newData.totalPrice);

  newData.products = newData.products?.map((item: any) => {
    return {
      name: item?.productDetail?.product.name,
      sku: item?.productDetail.sku,
      properties: item?.productDetail.properties,
      editedPrice: item?.editedPrice,
      price: item?.editedPrice ? item?.editedPrice : item?.price,
      qty: item?.productDetail?.qty,
      id: item?.productId,
      weight: item?.productDetail?.weight,
      count: item?.quantity,
      isCombo: item?.productDetail?.product?.isCombo,
      combo: item?.productDetail?.product?.combo || [],
    };
  });
  newData.carrier = {
    ...newData.carrier,
    orderId: newData.id,
  };
  newData.sourceType = dataDetail?.externalSource?.entity;
  if (dataDetail.teamInCharge === 1)
    newData.saleId = userId || newData.saleId || newData.carePageId;
  else newData.carePageId = userId || newData.saleId || newData.carePageId;
  newData.percentage = total
    ? Number(((Number(newData?.discount) / total) * 100).toFixed(2))
    : 0;
  newData.tagIds = newData?.tags?.map((item: any) => item.id);
  newData.sourceType = newData?.externalSource?.entity;
  newData.originalSourceId = newData?.sourceId;
  newData.sourceId = newData?.sourceProjectId
    ? dataDetail?.sourceId
    : [SourceEntity.other, SourceEntity.shopify].includes(
        dataDetail?.externalSource?.entity,
      )
    ? Number(dataDetail?.externalSource?.entityId)
    : dataDetail?.externalSource?.entityId;
  if (newData.carriers) delete newData.carriers;
  return newData;
};

export const formatSourceOfData = (dataDetail: Order, userId?: any) => {
  const newData = cloneDeep(dataDetail);
  newData.sourceType = dataDetail?.externalSource?.entity;
  newData.sourceType = newData?.externalSource?.entity;
  newData.sourceId = newData?.sourceProjectId
    ? dataDetail?.sourceId
    : [SourceEntity.other, SourceEntity.shopify].includes(
        dataDetail?.externalSource?.entity,
      )
    ? Number(dataDetail?.externalSource?.entityId)
    : dataDetail?.externalSource?.entityId;
  return newData;
};

export const formatDataAs = (dataDetail: Order, userId?: any) => {
  const newData = cloneDeep(dataDetail);
  const total = Number(newData.totalPrice);

  newData.products = newData.products?.map((item: any) => {
    return {
      name: item?.productDetail?.product.name,
      sku: item?.productDetail.sku,
      properties: item?.productDetail.properties,
      editedPrice: item?.editedPrice,
      price: item?.editedPrice ? item?.editedPrice : item?.price,
      qty: item?.productDetail?.qty,
      id: item?.productId,
      weight: item?.productDetail?.weight,
      count: item?.quantity,
      isCombo: item?.productDetail?.product?.isCombo,
      combo: item?.productDetail?.product?.combo || [],
    };
  });
  newData.carrier = {
    ...newData.carrier,
    orderId: newData.id,
  };
  newData.sourceType = dataDetail?.externalSource?.entity;
  newData.saleId = userId || newData.saleId || newData.carePageId;
  newData.percentage = total
    ? Number(((Number(newData?.discount) / total) * 100).toFixed(2))
    : 0;
  newData.tagIds = newData?.tags?.map((item: any) => item.id);
  newData.sourceType = newData?.externalSource?.entity;
  newData.originalSourceId = newData?.sourceId;
  newData.sourceId =
    newData.creatorId === ID_USER_SYSTEM
      ? newData.sourceId
      : dataDetail?.externalSource?.entityId;
  if (newData.carriers) delete newData.carriers;
  return newData;
};
export const duplicateDataInOrder = (dataDetail: any) => {
  const newData: any = {};
  const total = Number(newData.totalPrice);

  newData.products = dataDetail.products?.map((item: any) => {
    return {
      name: item?.productDetail?.product.name,
      sku: item?.productDetail.sku,
      properties: item?.productDetail.properties,
      editedPrice: item?.editedPrice,
      price: item?.editedPrice ? item?.editedPrice : item?.price,
      qty: item?.productDetail?.qty,
      id: item?.productId,
      weight: item?.productDetail?.weight,
      count: item?.quantity,
    };
  });

  newData.saleId = dataDetail.saleId || dataDetail.carePageId;
  newData.sourceId = dataDetail.sourceId;
  newData.printNotes = dataDetail.printNotes;
  newData.addressDistrictId = dataDetail.addressDistrictId;
  newData.addressNote = dataDetail.addressNote;
  newData.addressProvinceId = dataDetail.addressProvinceId;
  newData.addressText = dataDetail.addressText;
  newData.addressText = dataDetail.addressText;
  newData.addressWardId = dataDetail.addressWardId;
  newData.customerName = dataDetail.customerName;
  newData.customerPhone = dataDetail.customerPhone;
  newData.discountType = dataDetail.discountType;
  newData.projectId = dataDetail.projectId;
  newData.shippingFee = dataDetail.shippingFee;
  newData.surcharge = dataDetail.surcharge;
  newData.discount = dataDetail.discount;
  newData.paid = dataDetail.paid;
  newData.marketerId = dataDetail.marketerId;
  newData.note = dataDetail.note;
  newData.tagIds = dataDetail?.tags?.map((item: any) => item.id);
  newData.status = OrderStatusEnum.New;
  newData.percentage = dataDetail?.discount
    ? Number(((Number(dataDetail?.discount) / total) * 100).toFixed(2))
    : 0;
  newData.externalSource = dataDetail?.externalSource;
  newData.sourceType = dataDetail?.externalSource?.entity;
  newData.sourceId = [SourceEntity.other, SourceEntity.shopify].includes(
    dataDetail?.externalSource?.entity,
  )
    ? Number(dataDetail?.externalSource?.entityId)
    : dataDetail?.externalSource?.entityId;
  newData.carrier = {
    customerEDD: dataDetail.carrier?.customerEDD,
    waybillNote: dataDetail.carrier?.waybillNote,
  };
  newData.type = dataDetail.type;
  return newData;
};
